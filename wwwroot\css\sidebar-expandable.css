/* Expandable Sidebar Styles */

/* Expandable button styles */
.sidebar-expandable {
  position: relative;
}

.expandable-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.expandable-toggle.active .expand-icon {
  transform: translateY(-50%) rotate(90deg);
  opacity: 1;
}

.expandable-toggle:hover .expand-icon {
  opacity: 1;
}

/* Expanded sidebar container */
.sidebar-expanded {
  position: fixed;
  left: 80px;
  top: 0;
  bottom: 0;
  width: 320px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 999;
  transform: translateX(-100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;

  /* Debug styles */
  border: 2px solid red !important;
}

.sidebar-expanded.show {
  transform: translateX(0);
  /* Debug styles */
  border: 2px solid green !important;
}

.sidebar-expanded-content {
  height: 100%;
  overflow-y: auto;
  padding: 20px 0;
}

/* Expanded menu styles */
.expanded-menu {
  display: none;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.expanded-menu.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.expanded-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.expanded-menu-header h5 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.btn-close-expanded {
  background: none;
  border: none;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-close-expanded:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: #333;
}

.expanded-menu-body {
  padding: 0 10px;
}

/* Expanded menu items */
.expanded-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin: 5px 0;
  border-radius: 12px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.expanded-menu-item:hover {
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.1) 0%,
    rgba(76, 175, 80, 0.05) 100%
  );
  color: #4caf50;
  text-decoration: none;
  transform: translateX(5px);
}

.expanded-menu-item:active {
  transform: translateX(5px) scale(0.98);
}

.expanded-menu-item i {
  font-size: 18px;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.expanded-menu-item .item-title {
  font-weight: 500;
  font-size: 0.95rem;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

.expanded-menu-item .item-desc {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.2;
}

.expanded-menu-item:hover .item-desc {
  color: #4caf50;
}

/* Special styling for danger items */
.expanded-menu-item.text-danger {
  color: #dc3545;
}

.expanded-menu-item.text-danger:hover {
  background: linear-gradient(
    135deg,
    rgba(220, 53, 69, 0.1) 0%,
    rgba(220, 53, 69, 0.05) 100%
  );
  color: #dc3545;
}

.expanded-menu-item.text-danger:hover .item-desc {
  color: #dc3545;
}

/* Menu divider */
.expanded-menu-divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%
  );
  margin: 15px 15px;
}

/* Badge styling */
.expanded-menu-item .badge {
  font-size: 0.7rem;
  padding: 2px 6px;
}

/* Content wrapper adjustment when sidebar is expanded */
.wrapper.sidebar-expanded-active {
  margin-left: 400px;
  width: calc(100% - 400px);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Responsive styles */
@media (max-width: 1199px) {
  .sidebar-expanded {
    width: 280px;
  }

  .wrapper.sidebar-expanded-active {
    margin-left: 360px;
    width: calc(100% - 360px);
  }
}

@media (max-width: 991px) {
  .sidebar-expanded {
    left: 0;
    width: 100vw;
    z-index: 1060;
  }

  .wrapper.sidebar-expanded-active {
    margin-left: 0;
    width: 100%;
  }
}

/* Overlay for mobile */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Animation for menu items */
.expanded-menu.active .expanded-menu-item {
  animation: slideInUp 0.3s ease forwards;
}

.expanded-menu.active .expanded-menu-item:nth-child(1) {
  animation-delay: 0.1s;
}
.expanded-menu.active .expanded-menu-item:nth-child(2) {
  animation-delay: 0.15s;
}
.expanded-menu.active .expanded-menu-item:nth-child(3) {
  animation-delay: 0.2s;
}
.expanded-menu.active .expanded-menu-item:nth-child(4) {
  animation-delay: 0.25s;
}
.expanded-menu.active .expanded-menu-item:nth-child(5) {
  animation-delay: 0.3s;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling */
.sidebar-expanded-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-expanded-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-expanded-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.sidebar-expanded-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
