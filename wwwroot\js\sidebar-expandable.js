// Expandable Sidebar Functionality
document.addEventListener("DOMContentLoaded", function () {
    initializeExpandableSidebar();
});

function initializeExpandableSidebar() {
    const expandableButtons = document.querySelectorAll('.expandable-toggle');
    const sidebarExpanded = document.getElementById('sidebar-expanded');
    const wrapper = document.querySelector('.wrapper');
    const closeButtons = document.querySelectorAll('.btn-close-expanded');
    
    // Create overlay for mobile
    createOverlay();
    
    // Add click event listeners to expandable buttons
    expandableButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const target = this.getAttribute('data-target');
            toggleExpandedMenu(target);
        });
    });
    
    // Add click event listeners to close buttons
    closeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            closeExpandedSidebar();
        });
    });
    
    // Close sidebar when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.sidebar-nav') && 
            !e.target.closest('.sidebar-expanded') && 
            sidebarExpanded.classList.contains('show')) {
            closeExpandedSidebar();
        }
    });
    
    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebarExpanded.classList.contains('show')) {
            closeExpandedSidebar();
        }
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 991 && sidebarExpanded.classList.contains('show')) {
            // On mobile, ensure overlay is shown
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) {
                overlay.classList.add('show');
            }
        }
    });
}

function toggleExpandedMenu(targetId) {
    const sidebarExpanded = document.getElementById('sidebar-expanded');
    const targetMenu = document.getElementById(targetId);
    const wrapper = document.querySelector('.wrapper');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (!targetMenu) {
        console.error('Target menu not found:', targetId);
        return;
    }
    
    // Check if sidebar is already open with this menu
    const isCurrentMenuActive = targetMenu.classList.contains('active');
    const isSidebarOpen = sidebarExpanded.classList.contains('show');
    
    if (isCurrentMenuActive && isSidebarOpen) {
        // Close if clicking the same menu
        closeExpandedSidebar();
        return;
    }
    
    // Close all menus first
    closeAllMenus();
    
    // Remove active state from all buttons
    document.querySelectorAll('.expandable-toggle').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Add active state to clicked button
    const clickedButton = document.querySelector(`[data-target="${targetId}"]`);
    if (clickedButton) {
        clickedButton.classList.add('active');
    }
    
    // Show the sidebar
    sidebarExpanded.classList.add('show');
    
    // Show overlay on mobile
    if (window.innerWidth <= 991 && overlay) {
        overlay.classList.add('show');
    }
    
    // Adjust wrapper margin on desktop
    if (window.innerWidth > 991 && wrapper) {
        wrapper.classList.add('sidebar-expanded-active');
    }
    
    // Show the target menu with delay for smooth animation
    setTimeout(() => {
        targetMenu.classList.add('active');
    }, 100);
    
    console.log('Expanded menu opened:', targetId);
}

function closeExpandedSidebar() {
    const sidebarExpanded = document.getElementById('sidebar-expanded');
    const wrapper = document.querySelector('.wrapper');
    const overlay = document.querySelector('.sidebar-overlay');
    
    // Remove active state from all buttons
    document.querySelectorAll('.expandable-toggle').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Close all menus
    closeAllMenus();
    
    // Hide sidebar
    sidebarExpanded.classList.remove('show');
    
    // Hide overlay
    if (overlay) {
        overlay.classList.remove('show');
    }
    
    // Reset wrapper margin
    if (wrapper) {
        wrapper.classList.remove('sidebar-expanded-active');
    }
    
    console.log('Expanded sidebar closed');
}

function closeAllMenus() {
    const allMenus = document.querySelectorAll('.expanded-menu');
    allMenus.forEach(menu => {
        menu.classList.remove('active');
    });
}

function createOverlay() {
    // Check if overlay already exists
    if (document.querySelector('.sidebar-overlay')) {
        return;
    }
    
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    overlay.addEventListener('click', function() {
        closeExpandedSidebar();
    });
    
    document.body.appendChild(overlay);
}

// Handle menu item clicks
document.addEventListener('click', function(e) {
    if (e.target.closest('.expanded-menu-item')) {
        // Add click effect
        const item = e.target.closest('.expanded-menu-item');
        item.style.transform = 'translateX(5px) scale(0.98)';
        
        setTimeout(() => {
            item.style.transform = '';
        }, 150);
        
        // Close sidebar after navigation (optional)
        setTimeout(() => {
            closeExpandedSidebar();
        }, 300);
    }
});

// Export functions for external use
window.SidebarExpandable = {
    toggle: toggleExpandedMenu,
    close: closeExpandedSidebar,
    init: initializeExpandableSidebar
};

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
.expandable-toggle {
    transition: all 0.3s ease;
}

.expandable-toggle:hover {
    transform: translateY(-2px);
}

.expandable-toggle.active {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%) !important;
    color: white !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.4) !important;
}

.expanded-menu-item {
    transform: translateY(20px);
    opacity: 0;
    animation: none;
}

.expanded-menu.active .expanded-menu-item {
    animation: slideInUp 0.4s ease forwards;
}
`;

document.head.appendChild(style);
