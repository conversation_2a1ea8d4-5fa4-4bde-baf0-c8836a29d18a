/* Sidebar Navigation Styles */
.sidebar-nav {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: 80px;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 20px 0;
  backdrop-filter: blur(10px);
  overflow-y: auto;
}

/* Sidebar Sections */
.sidebar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin: 20px 0;
  width: 100%;
}

.sidebar-section.sidebar-actions {
  margin-top: auto;
  margin-bottom: 20px;
}

.sidebar-section.sidebar-social {
  margin-top: 0;
  margin-bottom: 20px;
}

.sidebar-nav-item {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background-color: white;
  border: 2px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  text-decoration: none;
}

.sidebar-nav-item:hover {
  text-decoration: none;
  color: white;
}

.sidebar-nav-item i {
  transition: all 0.3s ease;
}

.sidebar-nav-item:hover,
.sidebar-nav-item.active {
  background-color: #4caf50;
  border-color: #4caf50;
  transform: translateY(-5px);
  color: white;
  box-shadow: 0 8px 16px rgba(76, 175, 80, 0.3);
}

.sidebar-nav-item.active {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
}

.sidebar-nav-item:hover i,
.sidebar-nav-item.active i {
  transform: scale(1.1);
}

/* Special styling for dropdown buttons */
.sidebar-dropdown .sidebar-nav-item {
  position: relative;
}

.sidebar-dropdown .sidebar-nav-item::after {
  content: "";
  position: absolute;
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 0;
  background: linear-gradient(to bottom, #4caf50, #45a049);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.sidebar-dropdown .sidebar-nav-item:hover::after,
.sidebar-dropdown .sidebar-nav-item.active::after {
  height: 20px;
}

.sidebar-dropdown .sidebar-nav-item:hover {
  transform: translateY(-5px) translateX(3px);
}

/* Dropdown button active state */
.sidebar-dropdown .sidebar-nav-item.show {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: white;
  transform: translateY(-5px) translateX(3px);
  box-shadow: 0 8px 16px rgba(76, 175, 80, 0.4);
}

.sidebar-dropdown .sidebar-nav-item.show::after {
  height: 25px;
  background: linear-gradient(to bottom, #ffffff, #f0f0f0);
}

.sidebar-nav-item .tooltip {
  position: absolute;
  left: 70px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #4caf50;
  color: white;
  padding: 8px 15px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.sidebar-nav-item .tooltip:before {
  content: "";
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 6px 6px 6px 0;
  border-style: solid;
  border-color: transparent #4caf50 transparent transparent;
}

.sidebar-nav-item:hover .tooltip {
  opacity: 1;
  visibility: visible;
  left: 80px;
}

/* Sidebar Dropdown */
.sidebar-dropdown {
  position: relative;
  overflow: visible;
}

.sidebar-dropdown .dropdown-toggle::after {
  display: none;
}

/* Ensure dropdown container doesn't clip */
.sidebar-nav {
  overflow: visible;
}

.sidebar-section {
  overflow: visible;
}

.sidebar-dropdown-menu {
  position: absolute !important;
  left: 60px !important;
  top: 0 !important;
  bottom: auto !important;
  right: auto !important;
  min-width: 280px;
  max-width: 400px;
  background-color: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 0;
  z-index: 9999 !important;
  margin: 0 !important;
  inset: auto !important;

  /* Animation properties */
  display: none;
  visibility: hidden;
  opacity: 0;
  transform: translateX(-20px) scale(0.9);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Show dropdown animation */
.sidebar-dropdown-menu.show,
.sidebar-dropdown .show > .sidebar-dropdown-menu,
.sidebar-dropdown.show .sidebar-dropdown-menu {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  transform: translateX(0) scale(1) !important;
}

/* Dropdown button states */
.sidebar-dropdown .dropdown-toggle.show,
.sidebar-dropdown .dropdown-toggle:focus {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%) !important;
  color: white !important;
  transform: translateY(-5px) translateX(3px) !important;
  box-shadow: 0 8px 16px rgba(76, 175, 80, 0.4) !important;
}

/* Prevent dropdown from being cut off */
.sidebar-dropdown-menu {
  will-change: transform, opacity;
  pointer-events: auto;
  /* Ensure dropdown is always on top */
  position: absolute !important;
  z-index: 9999 !important;
}

.sidebar-dropdown-menu.show {
  pointer-events: auto;
  /* Force display and visibility */
  display: block !important;
  visibility: visible !important;
}

/* Fix for Bootstrap dropdown conflicts */
.sidebar-dropdown .dropdown-menu {
  position: static;
  display: none;
}

.sidebar-dropdown .dropdown-menu.show {
  display: none !important;
}

/* Ensure sidebar doesn't interfere with dropdown */
.sidebar-nav {
  position: fixed;
  z-index: 1000;
}

.sidebar-dropdown {
  z-index: 1001;
}

.sidebar-dropdown-menu {
  z-index: 1002 !important;
}

.sidebar-dropdown-menu .dropdown-item {
  padding: 0.875rem 1.25rem;
  font-size: 0.95rem;
  color: #333;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0.125rem 0.5rem;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.sidebar-dropdown-menu .dropdown-item:hover {
  background-color: rgba(76, 175, 80, 0.15);
  color: #4caf50;
  transform: translateX(5px);
}

.sidebar-dropdown-menu .dropdown-item i {
  margin-right: 0.75rem;
  width: 16px;
  text-align: center;
}

.sidebar-dropdown-menu .dropdown-divider {
  margin: 0.75rem 0.5rem;
  border-color: rgba(0, 0, 0, 0.1);
}

.sidebar-dropdown-menu .badge {
  margin-left: auto;
  margin-right: 0.5rem;
}

/* Social Items */
.social-item.like {
  background-color: #ff5252 !important;
  border-color: #ff5252 !important;
  color: white !important;
}

.social-item.like:hover {
  background-color: #e53935 !important;
  border-color: #e53935 !important;
  color: white !important;
}

.social-item.share {
  background-color: #2196f3 !important;
  border-color: #2196f3 !important;
  color: white !important;
}

.social-item.share:hover {
  background-color: #1976d2 !important;
  border-color: #1976d2 !important;
  color: white !important;
}

/* Logo in sidebar */
.sidebar-logo {
  position: absolute;
  top: 20px;
  left: 0;
  width: 80px;
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.sidebar-logo a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.sidebar-logo a:hover {
  background-color: white;
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  text-decoration: none;
}

/* Full page layout styles */
.full-page-layout .sidebar-nav {
  z-index: 1050;
}

.full-page-layout .wrapper.full-width {
  margin-left: 80px;
  width: calc(100% - 80px);
}

/* Dropdown positioning for different screen sizes */
@media (min-width: 1400px) {
  .sidebar-dropdown-menu {
    min-width: 320px;
  }
}

@media (max-width: 1199px) {
  .sidebar-dropdown-menu {
    min-width: 260px;
    max-width: 350px;
  }
}

/* Responsive styles */
@media (max-width: 991px) {
  .sidebar-nav {
    display: none !important;
  }

  .wrapper,
  .full-page-layout .wrapper.full-width {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .sidebar-dropdown-menu {
    position: fixed !important;
    left: 10px !important;
    right: 10px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: auto !important;
    min-width: auto !important;
    max-width: none !important;
  }
}

/* Ensure dropdown doesn't go off-screen */
@media (max-height: 600px) {
  .sidebar-dropdown-menu {
    max-height: 400px;
    overflow-y: auto;
  }
}

@media (min-width: 992px) {
  .mobile-menu-toggle {
    display: none !important;
  }
}
